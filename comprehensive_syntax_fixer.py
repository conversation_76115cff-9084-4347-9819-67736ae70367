#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مصلح الأخطاء النحوية الشامل والمتقدم
Comprehensive Advanced Syntax Fixer
"""

import ast
import re
from pathlib import Path
from typing import List, Dict, Tuple
import traceback
from typing import List, Dict, Optional, Tuple, Any, Union, Callable

class ComprehensiveSyntaxFixer:
    """مصلح الأخطاء النحوية الشامل والمتقدم"""

    def __init__(self):
        self.project_root = Path(".")
        self.fixed_files = []
        self.errors = []

        # الملفات الحرجة التي تحتاج إصلاح فوري
        self.critical_files = [
            "ui/main_window.py",
            "main.py",
            "database/hybrid_database_manager.py",
            "services/sales_manager.py",
            "core/scheduler_manager.py"
        ]

    def analyze_syntax_errors(self, file_path: Path) -> List[Dict]:
        """تحليل الأخطاء النحوية في ملف واحد"""
        errors = []

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # محاولة تحليل الملف نحوياً
            try:
                ast.parse(content)
                return []  # لا توجد أخطاء
            except SyntaxError as e:
                errors.append({
                    'line': e.lineno,
                    'column': e.offset,
                    'error': str(e),
                    'text': e.text.strip() if e.text else ''
                })

        except Exception as e:
            print(f"❌ خطأ في قراءة {file_path}: {e}")

        return errors

    def fix_imports_in_wrong_places(self, content: str) -> str:
        """إصلاح الاستيرادات في أماكن خاطئة"""
        lines = content.split('\n')
        new_lines = []
        imports_to_move = []

        # البحث عن الاستيرادات في أماكن خاطئة
        for i, line in enumerate(lines):
            stripped = line.strip()

            # إذا كان السطر استيراد وليس في بداية الملف
            if (stripped.startswith('from ') or stripped.startswith('import ')) and i > 20:
                # التحقق من أنه ليس في بداية دالة أو كلاس
                indent = len(line) - len(line.lstrip())
                if indent > 0:  # استيراد داخل دالة أو كلاس
                    imports_to_move.append(stripped)
                    continue  # تجاهل هذا السطر

            new_lines.append(line)

        # إضافة الاستيرادات المنقولة في بداية الملف
        if imports_to_move:
            # العثور على موقع إدراج الاستيرادات
            insert_pos = 0
            for i, line in enumerate(new_lines):
                if line.strip().startswith(('import ', 'from ')):
                    insert_pos = i + 1
                elif line.strip() and not line.strip().startswith('#') and not '"""' in line:
                    break

            # إدراج الاستيرادات
            for imp in reversed(imports_to_move):
                new_lines.insert(insert_pos, imp)

        return '\n'.join(new_lines)

    def fix_incomplete_try_except_blocks(self, content: str) -> str:
        """إصلاح try/except blocks غير المكتملة"""
        lines = content.split('\n')
        new_lines = []
        i = 0

        while i < len(lines):
            line = lines[i]
            stripped = line.strip()

            # إذا وجدنا try:
            if stripped == 'try:':
                new_lines.append(line)
                indent = len(line) - len(line.lstrip())

                # البحث عن المحتوى والـ except/finally
                j = i + 1
                has_content = False
                has_except_or_finally = False
                block_lines = []

                while j < len(lines):
                    next_line = lines[j]
                    next_stripped = next_line.strip()

                    if not next_stripped:  # سطر فارغ
                        block_lines.append(next_line)
                        j += 1
                        continue

                    next_indent = len(next_line) - len(next_line.lstrip())

                    # إذا كان المحتوى داخل try
                    if next_indent > indent:
                        has_content = True
                        block_lines.append(next_line)
                    # إذا وجدنا except أو finally
                    elif next_stripped.startswith(('except', 'finally')) and next_indent == indent:
                        has_except_or_finally = True
                        block_lines.append(next_line)
                        j += 1
                        break
                    # إذا وصلنا لكود جديد بنفس المستوى أو أقل
                    elif next_indent <= indent:
                        break
                    else:
                        block_lines.append(next_line)

                    j += 1

                # إضافة المحتوى
                new_lines.extend(block_lines)

                # إذا لم يكن هناك محتوى، أضف pass
                if not has_content:
                    new_lines.append(' ' * (indent + 4) + 'pass')

                # إذا لم يكن هناك except أو finally، أضف except عام
                if not has_except_or_finally:
                    new_lines.append(' ' * indent + 'except Exception as e:')
                    new_lines.append(' ' * (indent + 4) + 'print(f"خطأ: {e}")')

                i = j - 1
            else:
                new_lines.append(line)

            i += 1

        return '\n'.join(new_lines)

    def fix_indentation_issues(self, content: str) -> str:
        """إصلاح مشاكل المسافات البادئة"""
        lines = content.split('\n')
        new_lines = []

        for line in lines:
            if line.strip():
                # تحويل tabs إلى spaces
                if '\t' in line:
                    line = line.replace('\t', '    ')

                # إصلاح المسافات البادئة غير المتسقة
                indent = len(line) - len(line.lstrip())
                if indent % 4 != 0:
                    correct_indent = ((indent + 3) // 4) * 4
                    line = ' ' * correct_indent + line.lstrip()

            new_lines.append(line)

        return '\n'.join(new_lines)

    def fix_unmatched_brackets(self, content: str) -> str:
        """إصلاح الأقواس غير المتطابقة"""
        # عد الأقواس
        open_parens = content.count('(')
        close_parens = content.count(')')
        open_brackets = content.count('[')
        close_brackets = content.count(']')
        open_braces = content.count('{')
        close_braces = content.count('}')

        # إصلاح الأقواس المفقودة
        if open_parens > close_parens:
            content += ')' * (open_parens - close_parens)
        elif close_parens > open_parens:
            content = '(' * (close_parens - open_parens) + content

        if open_brackets > close_brackets:
            content += ']' * (open_brackets - close_brackets)
        elif close_brackets > open_brackets:
            content = '[' * (close_brackets - open_brackets) + content

        if open_braces > close_braces:
            content += '}' * (open_braces - close_braces)
        elif close_braces > open_braces:
            content = '{' * (close_braces - open_braces) + content

        return content

    def fix_file_comprehensively(self, file_path: Path) -> bool:
        """إصلاح شامل لملف واحد"""
        print(f"\n🔧 إصلاح شامل: {file_path.name}")

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()

            content = original_content

            # تطبيق جميع الإصلاحات
            print("   📦 إصلاح الاستيرادات...")
            content = self.fix_imports_in_wrong_places(content)

            print("   🔧 إصلاح try/except blocks...")
            content = self.fix_incomplete_try_except_blocks(content)

            print("   📏 إصلاح المسافات البادئة...")
            content = self.fix_indentation_issues(content)

            print("   🔗 إصلاح الأقواس...")
            content = self.fix_unmatched_brackets(content)

            # التحقق من صحة النحو
            try:
                ast.parse(content)
                syntax_valid = True
                print("   ✅ النحو صحيح")
            except SyntaxError as e:
                syntax_valid = False
                print(f"   ⚠️ خطأ نحوي متبقي: {e}")

            # حفظ الملف إذا تم تحسينه
            if content != original_content or not syntax_valid:
                # إنشاء نسخة احتياطية
                backup_path = file_path.with_suffix(f"{file_path.suffix}.backup")
                with open(backup_path, 'w', encoding='utf-8') as f:
                    f.write(original_content)
                print(f"   💾 تم إنشاء نسخة احتياطية: {backup_path.name}")

                # حفظ الملف المصلح
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)

                status = "✅ تم الإصلاح بنجاح" if syntax_valid else "⚠️ تم التحسين جزئياً"
                print(f"   {status}")

                return True
            else:
                print("   ✅ الملف لا يحتاج إصلاح")
                return False

        except Exception as e:
            error_msg = f"خطأ في إصلاح {file_path}: {e}"
            print(f"   ❌ {error_msg}")
            self.errors.append(error_msg)
            return False

    def fix_critical_files(self):
        """إصلاح الملفات الحرجة"""
        print("🚨 بدء الإصلاح الشامل للملفات الحرجة...")
        print("=" * 60)

        fixed_count = 0

        for file_path_str in self.critical_files:
            file_path = Path(file_path_str)

            if not file_path.exists():
                print(f"⚠️ الملف غير موجود: {file_path}")
                continue

            # تحليل الأخطاء أولاً
            errors = self.analyze_syntax_errors(file_path)
            if errors:
                print(f"🔍 {file_path.name}: {len(errors)} خطأ نحوي")
                for error in errors:
                    print(f"   - السطر {error['line']}: {error['error']}")

            # تطبيق الإصلاح الشامل
            if self.fix_file_comprehensively(file_path):
                fixed_count += 1
                self.fixed_files.append(str(file_path))

        print(f"\n📊 النتائج النهائية:")
        print(f"   🔧 ملفات تم إصلاحها: {fixed_count}")
        print(f"   ❌ أخطاء حدثت: {len(self.errors)}")

        if self.errors:
            print(f"\n⚠️ الأخطاء:")
            for error in self.errors:
                print(f"   - {error}")

        return fixed_count

    def validate_all_fixes(self):
        """التحقق من صحة جميع الإصلاحات"""
        print(f"\n🔍 التحقق من صحة الإصلاحات...")

        valid_count = 0
        for file_path_str in self.fixed_files:
            file_path = Path(file_path_str)

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                try:
                    ast.parse(content)
                    valid_count += 1
                    print(f"   ✅ {file_path.name}: صحيح نحوياً")
                except SyntaxError as e:
                    print(f"   ⚠️ {file_path.name}: خطأ نحوي - السطر {e.lineno}: {e.msg}")

            except Exception as e:
                print(f"   ❌ {file_path.name}: خطأ في القراءة - {e}")

        print(f"\n📊 نتائج التحقق:")
        print(f"   ✅ ملفات صحيحة: {valid_count}")
        print(f"   ⚠️ ملفات تحتاج مراجعة: {len(self.fixed_files) - valid_count}")

        success_rate = (valid_count / len(self.fixed_files)) * 100 if self.fixed_files else 0
        print(f"   📈 معدل النجاح: {success_rate:.1f}%")

        return valid_count

def main():
    """الدالة الرئيسية"""
    print("🔬 مصلح الأخطاء النحوية الشامل والمتقدم")
    print("=" * 60)

    fixer = ComprehensiveSyntaxFixer()

    # إصلاح الملفات الحرجة
    fixed_count = fixer.fix_critical_files()

    # التحقق من الإصلاحات
    if fixed_count > 0:
        valid_count = fixer.validate_all_fixes()

        print(f"\n🎉 تم الانتهاء من الإصلاح الشامل!")
        print(f"📊 إجمالي الملفات المصلحة: {fixed_count}")
        print(f"✅ ملفات صحيحة نحوياً: {valid_count}")

        if valid_count == fixed_count:
            print("🎊 جميع الملفات أصبحت صحيحة نحوياً!")
        else:
            print("💡 بعض الملفات تحتاج مراجعة يدوية")
    else:
        print("✅ جميع الملفات الحرجة صحيحة نحوياً!")

    print(f"\n💡 يُنصح بإعادة تشغيل البرنامج للتأكد من عمله")

if __name__ == "__main__":
    main()
