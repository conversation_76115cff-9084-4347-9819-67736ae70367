# -*- coding: utf-8 -*-
"""
مدير قاعدة بيانات PostgreSQL
PostgreSQL Database Manager
"""

import psycopg2
import psycopg2.extras
import logging
import subprocess
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any, Union, Callable
from pathlib import Path
from contextlib import contextmanager
from typing import List, Dict, Optional, Tuple, Any, Union, Callable

class PostgreSQLManager:
    """مدير قاعدة بيانات PostgreSQL للمحاسبة"""

    def __init__(self, config: Dict = None):
        """
        تهيئة مدير PostgreSQL

        Args:
            config (Dict): إعدادات الاتصال
        """
        self.config = config or {
            'host': 'localhost',
            'port': 5432,
            'database': 'accounting_db',
            'user': 'postgres',
            'password': 'postgres'
        }

        self.logger = logging.getLogger(__name__)
        self._connection_pool = None

        # إنشاء قاعدة البيانات إذا لم تكن موجودة
        self.create_database_if_not_exists()

        # إنشاء الجداول
        self.create_tables()

        # إدراج البيانات التجريبية
        self.insert_sample_data()

    def create_database_if_not_exists(self):
        """إنشاء قاعدة البيانات إذا لم تكن موجودة"""
        try:
            # الاتصال بقاعدة postgres الافتراضية
            temp_config = self.config.copy()
            temp_config['database'] = 'postgres'

            conn = psycopg2.connect(**temp_config)
            conn.autocommit = True
            cursor = conn.cursor()

            # التحقق من وجود قاعدة البيانات
            cursor.execute(
                "SELECT 1 FROM pg_database WHERE datname = %s",
                (self.config['database'],)
            )

            if not cursor.fetchone():
                # إنشاء قاعدة البيانات
                cursor.execute(f"CREATE DATABASE {self.config['database']}")
                self.logger.info(f"تم إنشاء قاعدة البيانات: {self.config['database']}")
            else:
                self.logger.info(f"قاعدة البيانات موجودة: {self.config['database']}")

            cursor.close()
            conn.close()

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء قاعدة البيانات: {e}")

    @contextmanager
    def get_connection(self):
        """الحصول على اتصال بقاعدة البيانات"""
        conn = None
        try:
            conn = psycopg2.connect(**self.config)
            yield conn
        except Exception as e:
            pass
        except Exception as e:
            if conn:
                conn.rollback()
            self.logger.error(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            raise
        finally:
            if conn:
                conn.close()

    def create_tables(self):
        """إنشاء جداول قاعدة البيانات"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # جدول المستخدمين
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS users (
                        id SERIAL PRIMARY KEY,
                        username VARCHAR(50) UNIQUE NOT NULL,
                        password_hash VARCHAR(255) NOT NULL,
                        full_name VARCHAR(100),
                        role VARCHAR(20) DEFAULT 'user',
                        email VARCHAR(100),
                        phone VARCHAR(20),
                        is_active BOOLEAN DEFAULT TRUE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        last_login TIMESTAMP,
                        profile_image TEXT
                    )
                """)

                # جدول العملاء
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS customers (
                        id SERIAL PRIMARY KEY,
                        name VARCHAR(100) NOT NULL,
                        phone VARCHAR(20),
                        email VARCHAR(100),
                        address TEXT,
                        tax_number VARCHAR(50),
                        credit_limit DECIMAL(15,2) DEFAULT 0,
                        current_balance DECIMAL(15,2) DEFAULT 0,
                        customer_type VARCHAR(20) DEFAULT 'regular',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        is_active BOOLEAN DEFAULT TRUE
                    )
                """)

                # جدول الموردين
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS suppliers (
                        id SERIAL PRIMARY KEY,
                        name VARCHAR(100) NOT NULL,
                        phone VARCHAR(20),
                        email VARCHAR(100),
                        address TEXT,
                        tax_number VARCHAR(50),
                        current_balance DECIMAL(15,2) DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        is_active BOOLEAN DEFAULT TRUE
                    )
                """)

                # جدول المنتجات
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS products (
                        id SERIAL PRIMARY KEY,
                        name VARCHAR(100) NOT NULL,
                        barcode VARCHAR(50) UNIQUE,
                        category VARCHAR(50),
                        unit VARCHAR(20) DEFAULT 'قطعة',
                        cost_price DECIMAL(10,2) DEFAULT 0,
                        selling_price DECIMAL(10,2) DEFAULT 0,
                        min_stock DECIMAL(10,2) DEFAULT 0,
                        current_stock DECIMAL(10,2) DEFAULT 0,
                        stock INTEGER DEFAULT 0,
                        price DECIMAL(10,2) DEFAULT 0,
                        description TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        is_active BOOLEAN DEFAULT TRUE
                    )
                """)

                # جدول فواتير المبيعات المتقدمة
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS sales_invoices (
                        id SERIAL PRIMARY KEY,
                        invoice_number VARCHAR(50) UNIQUE NOT NULL,
                        customer_id INTEGER REFERENCES customers(id),
                        total_amount DECIMAL(15,2) NOT NULL,
                        discount_amount DECIMAL(15,2) DEFAULT 0,
                        tax_amount DECIMAL(15,2) DEFAULT 0,
                        net_amount DECIMAL(15,2) NOT NULL,
                        payment_status VARCHAR(20) DEFAULT 'pending',
                        invoice_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        due_date TIMESTAMP,
                        notes TEXT,
                        created_by INTEGER REFERENCES users(id)
                    )
                """)

                # جدول تفاصيل فواتير المبيعات المتقدمة
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS sales_invoice_items (
                        id SERIAL PRIMARY KEY,
                        invoice_id INTEGER NOT NULL REFERENCES sales_invoices(id),
                        product_id INTEGER NOT NULL REFERENCES products(id),
                        quantity DECIMAL(10,2) NOT NULL,
                        unit_price DECIMAL(10,2) NOT NULL,
                        total_price DECIMAL(15,2) NOT NULL
                    )
                """)

                # جدول الفواتير البسيط (للتوافق)
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS invoices (
                        id SERIAL PRIMARY KEY,
                        customer_name VARCHAR(100),
                        total DECIMAL(15,2),
                        date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # جدول تفاصيل الفواتير البسيط (للتوافق)
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS invoice_items (
                        id SERIAL PRIMARY KEY,
                        invoice_id INTEGER REFERENCES invoices(id),
                        product_id INTEGER REFERENCES products(id),
                        quantity INTEGER,
                        price DECIMAL(10,2)
                    )
                """)

                # جدول حركات الخزينة
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS treasury_transactions (
                        id SERIAL PRIMARY KEY,
                        transaction_type VARCHAR(20) NOT NULL,
                        amount DECIMAL(15,2) NOT NULL,
                        description TEXT,
                        reference_type VARCHAR(50),
                        reference_id INTEGER,
                        transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        created_by INTEGER REFERENCES users(id)
                    )
                """)

                # جدول حركات المخزون
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS inventory_movements (
                        id SERIAL PRIMARY KEY,
                        product_id INTEGER NOT NULL REFERENCES products(id),
                        movement_type VARCHAR(20) NOT NULL,
                        quantity DECIMAL(10,2) NOT NULL,
                        reference_type VARCHAR(50),
                        reference_id INTEGER,
                        notes TEXT,
                        movement_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        created_by INTEGER REFERENCES users(id)
                    )
                """)

                # جدول سجل الأنشطة
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS activity_logs (
                        id SERIAL PRIMARY KEY,
                        user_id INTEGER REFERENCES users(id),
                        action VARCHAR(100) NOT NULL,
                        table_name VARCHAR(50),
                        record_id INTEGER,
                        old_values JSONB,
                        new_values JSONB,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                conn.commit()
                self.logger.info("تم إنشاء جميع الجداول بنجاح")

        except Exception as e:
            pass
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء الجداول: {e}")
            raise

    def create_indexes(self):
        """إنشاء الفهارس لتحسين الأداء"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                indexes = [
                    "CREATE INDEX IF NOT EXISTS idx_products_name ON products(name)",
                    "CREATE INDEX IF NOT EXISTS idx_products_barcode ON products(barcode)",
                    "CREATE INDEX IF NOT EXISTS idx_products_category ON products(category)",
                    "CREATE INDEX IF NOT EXISTS idx_sales_invoices_number ON sales_invoices(invoice_number)",
                    "CREATE INDEX IF NOT EXISTS idx_sales_invoices_date ON sales_invoices(invoice_date)",
                    "CREATE INDEX IF NOT EXISTS idx_sales_invoices_customer ON sales_invoices(customer_id)",
                    "CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(name)",
                    "CREATE INDEX IF NOT EXISTS idx_suppliers_name ON suppliers(name)",
                    "CREATE INDEX IF NOT EXISTS idx_activity_logs_user ON activity_logs(user_id)",
                    "CREATE INDEX IF NOT EXISTS idx_activity_logs_timestamp ON activity_logs(timestamp)"
                ]

                for index_sql in indexes:
                    cursor.execute(index_sql)

                conn.commit()
                self.logger.info("تم إنشاء جميع الفهارس بنجاح")

        except Exception as e:
            pass
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء الفهارس: {e}")

    def insert_sample_data(self):
        """إدراج بيانات تجريبية"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # التحقق من وجود بيانات
                cursor.execute("SELECT COUNT(*) FROM users")
                if cursor.fetchone()[0] == 0:
                    # إدراج مستخدم افتراضي
                    cursor.execute("""
                        INSERT INTO users (username, password_hash, full_name, role)
                        VALUES (%s, %s, %s, %s)
                    """, ('admin', 'pbkdf2:sha256:260000$salt$hash', 'المدير العام', 'admin'))

                # التحقق من وجود منتجات
                cursor.execute("SELECT COUNT(*) FROM products")
                if cursor.fetchone()[0] == 0:
                    # إدراج منتجات تجريبية
                    products = [
                        ('كوكا كولا 330مل', '1234567890123', 'مشروبات', 'علبة', 120.0, 150.0, 10, 100, 100, 150.0),
                        ('بيبسي 330مل', '1234567890124', 'مشروبات', 'علبة', 115.0, 145.0, 10, 80, 80, 145.0),
                        ('عصير برتقال طبيعي', '1234567890125', 'مشروبات', 'زجاجة', 180.0, 220.0, 5, 50, 50, 220.0),
                        ('شيبس بطاطس', '1234567890126', 'وجبات خفيفة', 'كيس', 90.0, 120.0, 20, 60, 60, 120.0),
                        ('بسكويت شوكولاتة', '1234567890127', 'حلويات', 'علبة', 60.0, 80.0, 15, 40, 40, 80.0),
                        ('شوكولاتة داكنة', '1234567890128', 'حلويات', 'قطعة', 200.0, 250.0, 10, 30, 30, 250.0),
                        ('حليب كامل الدسم', '1234567890129', 'منتجات ألبان', 'لتر', 250.0, 300.0, 5, 25, 25, 300.0),
                        ('جبنة بيضاء', '1234567890130', 'منتجات ألبان', 'كيلو', 350.0, 400.0, 3, 15, 15, 400.0),
                        ('لبن زبادي', '1234567890131', 'منتجات ألبان', 'كوب', 150.0, 180.0, 10, 20, 20, 180.0),
                        ('خبز أبيض', '1234567890132', 'مخبوزات', 'رغيف', 25.0, 35.0, 50, 100, 100, 35.0),
                        ('كيك فانيليا', '1234567890133', 'حلويات', 'قطعة', 280.0, 350.0, 5, 12, 12, 350.0),
                        ('ماء معدني', '1234567890134', 'مشروبات', 'زجاجة', 35.0, 50.0, 20, 80, 80, 50.0)
                    ]

                    cursor.executemany("""
                        INSERT INTO products 
                        (name, barcode, category, unit, cost_price, selling_price, min_stock, current_stock, stock, price)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, products)

                conn.commit()
                self.logger.info("تم إدراج البيانات التجريبية بنجاح")

        except Exception as e:
            pass
        except Exception as e:
            self.logger.error(f"خطأ في إدراج البيانات التجريبية: {e}")

    def get_database_info(self) -> Dict:
        """الحصول على معلومات قاعدة البيانات"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                info = {}

                # عدد الجداول
                cursor.execute("""
                    SELECT COUNT(*) FROM information_schema.tables 
                    WHERE table_schema = 'public'
                """)
                info['tables_count'] = cursor.fetchone()[0]

                # عدد المنتجات
                cursor.execute("SELECT COUNT(*) FROM products WHERE is_active = TRUE")
                info['products_count'] = cursor.fetchone()[0]

                # عدد الفواتير
                cursor.execute("SELECT COUNT(*) FROM sales_invoices")
                info['invoices_count'] = cursor.fetchone()[0]

                # عدد العملاء
                cursor.execute("SELECT COUNT(*) FROM customers WHERE is_active = TRUE")
                info['customers_count'] = cursor.fetchone()[0]

                # حجم قاعدة البيانات
                cursor.execute("""
                    SELECT pg_size_pretty(pg_database_size(%s))
                """, (self.config['database'],))
                info['database_size'] = cursor.fetchone()[0]

                return info

        except Exception as e:
            pass
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على معلومات قاعدة البيانات: {e}")
            return {}

    def backup_database(self, backup_path: str = None) -> Optional[str]:
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            if not backup_path:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                backup_path = f"backups/postgresql_backup_{timestamp}.sql"

            # إنشاء مجلد النسخ الاحتياطية
            Path(backup_path).parent.mkdir(parents=True, exist_ok=True)

            # تنفيذ pg_dump
            cmd = [
                'pg_dump',
                '-h', self.config['host'],
                '-p', str(self.config['port']),
                '-U', self.config['user'],
                '-d', self.config['database'],
                '-f', backup_path,
                '--no-password'
            ]

            # تعيين متغير البيئة لكلمة المرور
            env = {'PGPASSWORD': self.config['password']}

            result = subprocess.run(cmd, env=env, capture_output=True, text=True)

            if result.returncode == 0:
                self.logger.info(f"تم إنشاء النسخة الاحتياطية: {backup_path}")
                return backup_path
            else:
                self.logger.error(f"فشل في إنشاء النسخة الاحتياطية: {result.stderr}")
                return None

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            return None
