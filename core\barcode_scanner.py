# -*- coding: utf-8 -*-
# cSpell:disable
"""
وحدة قارئ الباركود
Barcode Scanner Module
"""

import cv2
import numpy as np
from pyzbar import pyzbar
import threading
import time
import tkinter as tk
from tkinter import messagebox
from typing import Optional, Callable

class BarcodeScanner:
    """فئة قارئ الباركود"""

    def __init__(self, callback_function: Optional[Callable] = None):
        self.callback_function = callback_function
        self.is_scanning = False
        self.camera = None
        self.scan_thread = None
        self.last_scanned_code = None
        self.scan_delay = 2  # ثانيتين بين كل قراءة
        self.last_scan_time = 0

    def start_scanning(self, camera_index: int = 0):
        """بدء قراءة الباركود"""
        if self.is_scanning:
            return False

        try:
            # تشغيل الكاميرا
            self.camera = cv2.VideoCapture(camera_index)
            if not self.camera.isOpened():
                messagebox.showerror("خطأ", "لا يمكن الوصول إلى الكاميرا")
                return False

            # إعداد الكاميرا
            self.camera.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            self.camera.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)

            self.is_scanning = True

            # بدء خيط القراءة
            self.scan_thread = threading.Thread(target=self._scan_loop, daemon=True)
            self.scan_thread.start()

            return True

        except Exception as e:
            pass
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تشغيل قارئ الباركود: {e}")
            return False

    def stop_scanning(self):
        """إيقاف قراءة الباركود"""
        self.is_scanning = False

        if self.camera:
            self.camera.release()
            self.camera = None

        cv2.destroyAllWindows()

    def _scan_loop(self):
        """حلقة قراءة الباركود الرئيسية"""
        while self.is_scanning:
            try:
                # قراءة إطار من الكاميرا
                ret, frame = self.camera.read()
                if not ret:
                    continue

                # البحث عن باركود في الإطار
                barcodes = pyzbar.decode(frame)

                for barcode in barcodes:
                    # استخراج البيانات
                    barcode_data = barcode.data.decode('utf-8')
                    barcode_type = barcode.type

                    # التحقق من التأخير بين القراءات
                    current_time = time.time()
                    if (barcode_data != self.last_scanned_code or
                        current_time - self.last_scan_time > self.scan_delay):

                        self.last_scanned_code = barcode_data
                        self.last_scan_time = current_time

                        # رسم مربع حول الباركود
                        self._draw_barcode_box(frame, barcode)

                        # استدعاء دالة الاستجابة
                        if self.callback_function:
                            self.callback_function(barcode_data, barcode_type)

                # عرض الإطار
                cv2.imshow('قارئ الباركود - اضغط ESC للخروج', frame)

                # التحقق من الضغط على ESC للخروج
                if cv2.waitKey(1) & 0xFF == 27:  # ESC key:
                    break

            except Exception as e:
                pass
            except Exception as e:
                print(f"خطأ في قراءة الباركود: {e}")
                break

        self.stop_scanning()

    def _draw_barcode_box(self, frame, barcode):
        """رسم مربع حول الباركود المكتشف"""
        # الحصول على نقاط الباركود
        points = barcode.polygon

        if len(points) == 4:
            # رسم مربع
            pts = np.array(points, np.int32)
            pts = pts.reshape((-1, 1, 2))
            cv2.polylines(frame, [pts], True, (0, 255, 0), 3)

            # إضافة النص
            x = barcode.rect.left
            y = barcode.rect.top - 10
            cv2.putText(frame, barcode.data.decode('utf-8'), 
                        (x, y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

    def scan_single_barcode(self, timeout: int = 30) -> Optional[str]:
        """قراءة باركود واحد مع مهلة زمنية"""
        result = {"code": None}

        def callback(code, barcode_type):
            result["code"] = code
            self.stop_scanning()

        # حفظ دالة الاستجابة الحالية
        original_callback = self.callback_function
        self.callback_function = callback

        # بدء القراءة
        if self.start_scanning():
            # انتظار النتيجة أو انتهاء المهلة
            start_time = time.time()
            while self.is_scanning and time.time() - start_time < timeout:
                time.sleep(0.1)

            # إيقاف القراءة
            self.stop_scanning()

        # استعادة دالة الاستجابة الأصلية
        self.callback_function = original_callback

        return result["code"]

class BarcodeScannerWindow:
    """نافذة قارئ الباركود المستقلة"""

    def __init__(self, parent, callback_function: Optional[Callable] = None):
        self.parent = parent
        self.callback_function = callback_function
        self.scanner = BarcodeScanner(self._on_barcode_scanned)
        self.window = None
        self.scanned_codes = []

    def show_scanner_window(self):
        """عرض نافذة قارئ الباركود"""
        import customtkinter as ctk

        self.window = ctk.CTkToplevel(self.parent)
        self.window.title("📷 قارئ الباركود")
        self.window.geometry("500x400")
        self.window.configure(fg_color="#f0f0f0")

        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()

        # العنوان
        title_label = ctk.CTkLabel(
            self.window,
            text="📷 قارئ الباركود",
            font=("Arial", 20, "bold")
        )
        title_label.pack(pady=20)

        # التعليمات
        instructions_label = ctk.CTkLabel(
            self.window,
            text="وجه الكاميرا نحو الباركود\nسيتم إغلاق النافذة تلقائياً بعد القراءة",
            font=("Arial", 12),
            justify="center"
        )
        instructions_label.pack(pady=10)

        # حالة القراءة
        self.status_label = ctk.CTkLabel(
            self.window,
            text="جاري تشغيل الكاميرا...",
            font=("Arial", 12),
            text_color="blue"
        )
        self.status_label.pack(pady=10)

        # قائمة الأكواد المقروءة
        codes_frame = ctk.CTkFrame(self.window)
        codes_frame.pack(fill="both", expand=True, padx=20, pady=10)

        codes_title = ctk.CTkLabel(
            codes_frame,
            text="الأكواد المقروءة:",
            font=("Arial", 14, "bold")
        )
        codes_title.pack(pady=10)

        self.codes_listbox = tk.Listbox(
            codes_frame,
            font=("Arial", 12),
            height=8
        )
        self.codes_listbox.pack(fill="both", expand=True, padx=10, pady=10)

        # أزرار التحكم
        buttons_frame = ctk.CTkFrame(self.window)
        buttons_frame.pack(fill="x", padx=20, pady=10)

        # زر بدء القراءة
        self.start_btn = ctk.CTkButton(
            buttons_frame,
            text="▶️ بدء القراءة",
            command=self.start_scanning,
            fg_color="green",
            width=120
        )
        self.start_btn.pack(side="right", padx=5)

        # زر إيقاف القراءة
        self.stop_btn = ctk.CTkButton(
            buttons_frame,
            text="⏹️ إيقاف",
            command=self.stop_scanning,
            fg_color="red",
            width=120,
            state="disabled"
        )
        self.stop_btn.pack(side="right", padx=5)

        # زر إغلاق
        close_btn = ctk.CTkButton(
            buttons_frame,
            text="❌ إغلاق",
            command=self.close_window,
            fg_color="gray",
            width=120
        )
        close_btn.pack(side="left", padx=5)

        # ربط إغلاق النافذة
        self.window.protocol("WM_DELETE_WINDOW", self.close_window)

    def start_scanning(self):
        """بدء قراءة الباركود"""
        if self.scanner.start_scanning():
            self.status_label.configure(text="جاري القراءة... وجه الكاميرا نحو الباركود", text_color="green")
            self.start_btn.configure(state="disabled")
            self.stop_btn.configure(state="normal")
        else:
            self.status_label.configure(text="فشل في تشغيل الكاميرا", text_color="red")

    def stop_scanning(self):
        """إيقاف قراءة الباركود"""
        self.scanner.stop_scanning()
        self.status_label.configure(text="تم إيقاف القراءة", text_color="orange")
        self.start_btn.configure(state="normal")
        self.stop_btn.configure(state="disabled")

    def _on_barcode_scanned(self, code, barcode_type):
        """عند قراءة باركود جديد"""
        # إضافة الكود للقائمة
        self.scanned_codes.append(code)
        self.codes_listbox.insert(tk.END, f"{barcode_type}: {code}")
        self.codes_listbox.see(tk.END)

        # تحديث الحالة
        self.status_label.configure(text=f"تم قراءة: {code}", text_color="green")

        # استدعاء دالة الاستجابة الخارجية
        if self.callback_function:
            self.callback_function(code, barcode_type)

    def close_window(self):
        """إغلاق النافذة"""
        self.stop_scanning()
        if self.window:
            self.window.destroy()

def test_barcode_scanner():
    """اختبار قارئ الباركود"""
    def on_barcode_found(code, barcode_type):
        print(f"تم العثور على باركود: {code} (نوع: {barcode_type})")

    scanner = BarcodeScanner(on_barcode_found)

    print("بدء قراءة الباركود... اضغط ESC في نافذة الكاميرا للخروج")
    scanner.start_scanning()

    # انتظار حتى إيقاف القراءة
    try:
        while scanner.is_scanning:
            time.sleep(0.1)
    except KeyboardInterrupt:
        pass
    except KeyboardInterrupt:
        scanner.stop_scanning()
        print("تم إيقاف قارئ الباركود")

if __name__ == "__main__":
    test_barcode_scanner()
