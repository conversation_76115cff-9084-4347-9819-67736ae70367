{"timestamp": "2025-07-22T20:49:49.571910", "results": {"syntax_tests": [{"file": "main.py", "status": "PASS", "message": "تم تجميع الملف بنجاح"}, {"file": "START_HERE.py", "status": "PASS", "message": "تم تجميع الملف بنجاح"}, {"file": "run_app.py", "status": "PASS", "message": "تم تجميع الملف بنجاح"}, {"file": "safe_start.py", "status": "PASS", "message": "تم تجميع الملف بنجاح"}, {"file": "core/app_core.py", "status": "FAIL", "message": "  File \"core/app_core.py\", line 117\n    except:\n    ^^^^^^^^\nSyntaxError: default 'except:' must be last\n"}, {"file": "services/purchases_manager.py", "status": "PASS", "message": "تم تجميع الملف بنجاح"}], "import_tests": [{"module": "config.settings", "status": "PASS", "message": "تم استيراد إعدادات النظام بنجاح"}, {"module": "database.database_manager", "status": "PASS", "message": "تم استيراد مدير قاعدة البيانات بنجاح"}, {"module": "auth.auth_manager", "status": "PASS", "message": "تم استيراد مدير المصادقة بنجاح"}, {"module": "ui.main_window", "status": "PASS", "message": "تم استيراد النافذة الرئيسية بنجاح"}, {"module": "ui.central_control_panel", "status": "PASS", "message": "تم استيراد لوحة التحكم بنجاح"}, {"module": "themes.font_manager", "status": "PASS", "message": "تم استيراد مدير الخطوط بنجاح"}, {"module": "themes.theme_manager", "status": "PASS", "message": "تم استيراد مدير الثيمات بنجاح"}], "database_tests": [{"test": "connection", "status": "PASS", "message": "اتصال قاعدة البيانات يعمل بنجاح"}, {"test": "authentication", "status": "PASS", "message": "نظام المصادقة يعمل بنجاح"}], "ui_tests": [{"test": "customtkinter", "status": "PASS", "message": "customtkinter يعمل بنجاح"}, {"test": "arabic_fonts", "status": "PASS", "message": "الخطوط العربية تعمل بنجاح"}], "business_logic_tests": [{"test": "double_entry_balance", "status": "PASS", "message": "نظام القيد المزدوج يعمل بنجاح"}], "rtl_tests": [{"test": "rtl_config", "status": "PASS", "message": "إعدادات RTL مفعلة بنجاح"}]}, "summary": {"total_tests": 19, "passed_tests": 18, "failed_tests": 1, "success_rate": 94.73684210526315}}