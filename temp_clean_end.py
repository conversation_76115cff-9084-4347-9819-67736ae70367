#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة لتنظيف نهاية ملف central_control_panel.py
"""

# قراءة الملف
with open('ui/central_control_panel.py', 'r', encoding='utf-8') as f:
    lines = f.readlines()

# الاحتفاظ بالسطور حتى السطر 2851 فقط
clean_lines = lines[:2851]

# كتابة الملف المنظف
with open('ui/central_control_panel.py', 'w', encoding='utf-8') as f:
    f.writelines(clean_lines)

print(f"تم تنظيف الملف - تم الاحتفاظ بـ {len(clean_lines)} سطر")
print("آخر 5 أسطر:")
for i, line in enumerate(clean_lines[-5:], len(clean_lines)-4):
    print(f"{i}: {line.rstrip()}")
