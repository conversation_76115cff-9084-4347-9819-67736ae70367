# -*- coding: utf-8 -*-
# cSpell:disable
"""
اختبار نافذة إدارة الموارد البشرية
"""

import customtkinter as ctk
import sys
import os

# إضافة المسار الحالي للبحث عن الوحدات
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from ui.hr_management_window import HRManagementWindow
    from database.hybrid_database_manager import HybridDatabaseManager
except ImportError as e:
    print(f"خطأ في الاستيراد: {e}")
    sys.exit(1)

def main():
    """الدالة الرئيسية للاختبار"""
    # إعداد customtkinter
    ctk.set_appearance_mode("light")
    ctk.set_default_color_theme("blue")

    # إنشاء النافذة الرئيسية
    root = ctk.CTk()
    root.title("اختبار نافذة الموارد البشرية")
    root.geometry("800x600")

    # إنشاء مدير قاعدة البيانات
    try:
        db_manager = HybridDatabaseManager()
        print("تم تهيئة قاعدة البيانات بنجاح")
    except Exception as e:
        print(f"خطأ في تهيئة قاعدة البيانات: {e}")
        db_manager = None

    # زر لفتح نافذة الموارد البشرية
    def open_hr_window():
        try:
            hr_window = HRManagementWindow(root, db_manager)
            print("تم فتح نافذة الموارد البشرية بنجاح")
        except Exception as e:
            print(f"خطأ في فتح نافذة الموارد البشرية: {e}")
            import traceback
import traceback
            traceback.print_exc()

    # إنشاء زر الاختبار
    test_button = ctk.CTkButton(
        root,
        text="🧪 اختبار نافذة الموارد البشرية",
        font=ctk.CTkFont(size=16, weight="bold"),
        width=300,
        height=50,
        command=open_hr_window
    )
    test_button.pack(expand=True)

    # تشغيل التطبيق
    print("بدء تشغيل اختبار نافذة الموارد البشرية...")
    root.mainloop()

if __name__ == "__main__":
    main()
