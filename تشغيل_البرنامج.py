#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل برنامج ست الكل للمحاسبة
Simple Launcher for Accounting Software
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """تشغيل البرنامج الرئيسي"""
    print("🚀 مرحباً بك في برنامج ست الكل للمحاسبة")
    print("=" * 50)
    
    try:
        # تحقق من المتطلبات
        import customtkinter as ctk
        print("✅ customtkinter متوفر")
        
        # إعداد المظهر
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")
        
        # إنشاء النافذة الرئيسية
        root = ctk.CTk()
        root.title("🏦 برنامج ست الكل للمحاسبة")
        root.geometry("1400x900")
        
        # محاولة ملء الشاشة
        try:
            root.state('zoomed')
        except:
            try:
                root.attributes('-zoomed', True)
            except:
                pass
        
        print("🎯 اختر طريقة التشغيل:")
        print("1. النافذة الرئيسية للمحاسبة")
        print("2. لوحة التحكم المتقدمة")
        
        # اختيار افتراضي - النافذة الرئيسية
        choice = "1"
        
        if choice == "1":
            print("📊 تشغيل النافذة الرئيسية...")
            from ui.main_window import MainApplication
            
            # إغلاق النافذة المؤقتة
            root.destroy()
            
            # تشغيل التطبيق الرئيسي
            app = MainApplication()
            app.run()
            
        elif choice == "2":
            print("🎛️ تشغيل لوحة التحكم...")
            from ui.central_control_panel import CentralControlPanel
            
            # إنشاء لوحة التحكم
            control_panel = CentralControlPanel(root)
            
            print("✅ تم تشغيل لوحة التحكم بنجاح!")
            print("🔑 بيانات تسجيل الدخول:")
            print("   اسم المستخدم: 123")
            print("   كلمة المرور: 123")
            
            # تشغيل التطبيق
            root.mainloop()
        
        print("🎉 تم إغلاق البرنامج بنجاح!")
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 تأكد من تثبيت المتطلبات: pip install customtkinter")
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل البرنامج: {e}")
        import traceback
import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
