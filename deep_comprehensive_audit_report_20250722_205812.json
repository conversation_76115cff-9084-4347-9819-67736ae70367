{"timestamp": "2025-07-22T20:58:12.840535", "overall_score": 57.61904761904761, "status": "يحتاج تحسين ❌", "detailed_results": {"code_quality": {"total_files": 143, "total_lines": 60465, "total_functions": 1763, "total_classes": 98, "complexity_issues": [{"file": "comprehensive_functional_tester.py", "function": "test_integration", "complexity": 20}, {"file": "comprehensive_functional_tester.py", "function": "test_performance", "complexity": 17}, {"file": "comprehensive_import_fixer.py", "function": "analyze_file_imports", "complexity": 17}, {"file": "comprehensive_import_fixer.py", "function": "find_import_position", "complexity": 15}, {"file": "comprehensive_syntax_fixer.py", "function": "fix_imports_in_wrong_places", "complexity": 13}, {"file": "comprehensive_syntax_fixer.py", "function": "fix_incomplete_try_except_blocks", "complexity": 11}, {"file": "comprehensive_systematic_audit.py", "function": "audit_project_structure", "complexity": 24}, {"file": "critical_file_fixer.py", "function": "fix_comprehensive_income_window", "complexity": 12}, {"file": "critical_file_fixer.py", "function": "fix_except_blocks", "complexity": 11}, {"file": "deep_comprehensive_system_audit.py", "function": "analyze_code_quality", "complexity": 14}, {"file": "deep_comprehensive_system_audit.py", "function": "analyze_security", "complexity": 13}, {"file": "deep_comprehensive_system_audit.py", "function": "generate_comprehensive_report", "complexity": 11}, {"file": "except_block_fixer.py", "function": "fix_except_blocks_in_file", "complexity": 14}, {"file": "final_cleanup_tool.py", "function": "fix_indentation_issues", "complexity": 13}, {"file": "final_cleanup_tool.py", "function": "fix_incomplete_blocks", "complexity": 13}, {"file": "fix_remaining_imports.py", "function": "fix_imports", "complexity": 11}, {"file": "safe_main.py", "function": "main", "complexity": 11}, {"file": "syntax_error_fixer.py", "function": "fix_incomplete_try_blocks", "complexity": 11}, {"file": "core\\app_core.py", "function": "import_data", "complexity": 11}, {"file": "database\\journal_entries_manager.py", "function": "_validate_entry_data", "complexity": 12}, {"file": "database\\profit_loss_structure_manager.py", "function": "generate_structured_statement_text", "complexity": 16}, {"file": "database\\warehouse_manager.py", "function": "_validate_warehouse_data", "complexity": 21}, {"file": "services\\postgresql_sales_manager.py", "function": "_validate_sale_data", "complexity": 12}, {"file": "services\\sales_manager.py", "function": "_validate_sale_data", "complexity": 12}, {"file": "ui\\add_items_window.py", "function": "validate_form", "complexity": 27}, {"file": "ui\\add_items_window.py", "function": "get_form_data", "complexity": 11}, {"file": "ui\\central_control_panel.py", "function": "open_central_control_panel", "complexity": 16}, {"file": "ui\\central_control_panel.py", "function": "show_section", "complexity": 15}, {"file": "ui\\enhanced_pos_window.py", "function": "calculator_button_click", "complexity": 19}, {"file": "ui\\enhanced_pos_window.py", "function": "process_payment", "complexity": 11}, {"file": "ui\\hr_management_window.py", "function": "load_employee_details", "complexity": 13}, {"file": "ui\\hr_management_window.py", "function": "save_employee", "complexity": 11}, {"file": "ui\\hr_management_window.py", "function": "load_attendance_data", "complexity": 13}, {"file": "ui\\inventory_window.py", "function": "add_item", "complexity": 13}, {"file": "ui\\invoices_main_window.py", "function": "update_stats", "complexity": 18}, {"file": "ui\\journal_entries_window.py", "function": "add_detail_line", "complexity": 11}, {"file": "ui\\main_window.py", "function": "load_icon_image", "complexity": 11}, {"file": "ui\\main_window.py", "function": "load_icon_image_no_bg", "complexity": 12}, {"file": "ui\\main_window.py", "function": "show_main_dropdown_menu", "complexity": 24}, {"file": "ui\\sales_window.py", "function": "save_invoice", "complexity": 13}, {"file": "ui\\stock_management_window.py", "function": "validate_movement_form", "complexity": 11}, {"file": "ui\\warehouses_management_window.py", "function": "validate_data_integrity", "complexity": 11}, {"file": "ui\\warehouses_management_window.py", "function": "switch_view", "complexity": 21}], "documentation_coverage": 90.64095292115711, "syntax_errors": [{"file": "advanced_error_analyzer.py", "error": "unterminated string literal (detected at line 103) (<unknown>, line 103)", "line": 103}, {"file": "advanced_error_fixer.py", "error": "unterminated string literal (detected at line 147) (<unknown>, line 147)", "line": 147}, {"file": "advanced_syntax_fixer.py", "error": "unterminated string literal (detected at line 159) (<unknown>, line 159)", "line": 159}, {"file": "comprehensive_income_formula_demo.py", "error": "unexpected indent (<unknown>, line 254)", "line": 254}, {"file": "deep_comprehensive_fixer.py", "error": "unexpected character after line continuation character (<unknown>, line 170)", "line": 170}, {"file": "deep_import_fixer.py", "error": "unterminated string literal (detected at line 202) (<unknown>, line 202)", "line": 202}, {"file": "quick_pattern_fixer.py", "error": "invalid syntax (<unknown>, line 35)", "line": 35}, {"file": "run_fixed_app.py", "error": "unexpected indent (<unknown>, line 155)", "line": 155}, {"file": "start_with_scheduler.py", "error": "unexpected indent (<unknown>, line 81)", "line": 81}, {"file": "ultimate_system_fixer.py", "error": "unterminated string literal (detected at line 302) (<unknown>, line 302)", "line": 302}, {"file": "config\\postgresql_config.py", "error": "unexpected indent (<unknown>, line 151)", "line": 151}, {"file": "database\\comprehensive_income_manager.py", "error": "invalid syntax (<unknown>, line 274)", "line": 274}, {"file": "database\\fix_database.py", "error": "unterminated string literal (detected at line 69) (<unknown>, line 69)", "line": 69}, {"file": "ui\\backup_restore.py", "error": "unexpected indent (<unknown>, line 710)", "line": 710}, {"file": "ui\\daily_journal_window.py", "error": "invalid syntax (<unknown>, line 686)", "line": 686}, {"file": "ui\\sales_analysis_window.py", "error": "unexpected indent (<unknown>, line 1340)", "line": 1340}], "import_issues": [{"file": "advanced_error_analyzer.py", "error": "خطأ في تحليل الاستيرادات: unterminated string literal (detected at line 103) (<unknown>, line 103)"}, {"file": "advanced_error_fixer.py", "error": "خطأ في تحليل الاستيرادات: unterminated string literal (detected at line 147) (<unknown>, line 147)"}, {"file": "advanced_syntax_fixer.py", "error": "خطأ في تحليل الاستيرادات: unterminated string literal (detected at line 159) (<unknown>, line 159)"}, {"file": "comprehensive_income_formula_demo.py", "error": "خطأ في تحليل الاستيرادات: unexpected indent (<unknown>, line 254)"}, {"file": "deep_comprehensive_fixer.py", "error": "خطأ في تحليل الاستيرادات: unexpected character after line continuation character (<unknown>, line 170)"}, {"file": "deep_import_fixer.py", "error": "خطأ في تحليل الاستيرادات: unterminated string literal (detected at line 202) (<unknown>, line 202)"}, {"file": "quick_pattern_fixer.py", "error": "خطأ في تحليل الاستيرادات: invalid syntax (<unknown>, line 35)"}, {"file": "run_fixed_app.py", "error": "خطأ في تحليل الاستيرادات: unexpected indent (<unknown>, line 155)"}, {"file": "start_with_scheduler.py", "error": "خطأ في تحليل الاستيرادات: unexpected indent (<unknown>, line 81)"}, {"file": "ultimate_system_fixer.py", "error": "خطأ في تحليل الاستيرادات: unterminated string literal (detected at line 302) (<unknown>, line 302)"}, {"file": "config\\postgresql_config.py", "error": "خطأ في تحليل الاستيرادات: unexpected indent (<unknown>, line 151)"}, {"file": "database\\comprehensive_income_manager.py", "error": "خطأ في تحليل الاستيرادات: invalid syntax (<unknown>, line 274)"}, {"file": "database\\fix_database.py", "error": "خطأ في تحليل الاستيرادات: unterminated string literal (detected at line 69) (<unknown>, line 69)"}, {"file": "ui\\backup_restore.py", "error": "خطأ في تحليل الاستيرادات: unexpected indent (<unknown>, line 710)"}, {"file": "ui\\daily_journal_window.py", "error": "خطأ في تحليل الاستيرادات: invalid syntax (<unknown>, line 686)"}, {"file": "ui\\sales_analysis_window.py", "error": "خطأ في تحليل الاستيرادات: unexpected indent (<unknown>, line 1340)"}], "unused_imports": [], "code_duplications": []}, "security_analysis": {"sql_injection_risks": [{"file": "comprehensive_systematic_audit.py", "line": 605, "pattern": "execute\\s*\\(\\s*f[\"\\'].*{.*}.*[\"\\']", "code": "execute(f\"SELECT COUNT(*) FROM {table_name}\""}, {"file": "comprehensive_systematic_audit.py", "line": 609, "pattern": "execute\\s*\\(\\s*f[\"\\'].*{.*}.*[\"\\']", "code": "execute(f\"PRAGMA table_info({table_name})\""}, {"file": "comprehensive_systematic_audit.py", "line": 627, "pattern": "execute\\s*\\(\\s*f[\"\\'].*{.*}.*[\"\\']", "code": "execute(f\"PRAGMA foreign_key_list({table_name})\""}, {"file": "database_analyzer.py", "line": 133, "pattern": "execute\\s*\\(\\s*[\"\\'].*%.*[\"\\']", "code": "execute(\"SELECT name, tbl_name, sql FROM sqlite_master WHERE type='index' AND name NOT LIKE 'sqlite_%'\""}, {"file": "database_analyzer.py", "line": 92, "pattern": "execute\\s*\\(\\s*f[\"\\'].*{.*}.*[\"\\']", "code": "execute(f\"SELECT COUNT(*) FROM {table_name}\""}, {"file": "database_analyzer.py", "line": 96, "pattern": "execute\\s*\\(\\s*f[\"\\'].*{.*}.*[\"\\']", "code": "execute(f\"PRAGMA table_info({table_name})\""}, {"file": "database_analyzer.py", "line": 100, "pattern": "execute\\s*\\(\\s*f[\"\\'].*{.*}.*[\"\\']", "code": "execute(f\"SELECT * FROM {table_name} LIMIT 1\""}, {"file": "deep_comprehensive_system_audit.py", "line": 363, "pattern": "execute\\s*\\(\\s*f[\"\\'].*{.*}.*[\"\\']", "code": "execute(f\"PRAGMA table_info({table_name})\""}, {"file": "deep_comprehensive_system_audit.py", "line": 367, "pattern": "execute\\s*\\(\\s*f[\"\\'].*{.*}.*[\"\\']", "code": "execute(f\"SELECT COUNT(*) FROM {table_name}\""}, {"file": "deep_comprehensive_system_audit.py", "line": 420, "pattern": "execute\\s*\\(\\s*f[\"\\'].*{.*}.*[\"\\']", "code": "execute(f\"SELECT COUNT(*) FROM {table_name} WHERE {col['name']} IS NULL\""}, {"file": "deep_comprehensive_system_audit.py", "line": 434, "pattern": "execute\\s*\\(\\s*f[\"\\'].*{.*}.*[\"\\']", "code": "execute(f\"SELECT {pk_col}, COUNT(*) FROM {table_name} GROUP BY {pk_col} HAVING COUNT(*) > 1\""}, {"file": "database\\fix_database.py", "line": 35, "pattern": "execute\\s*\\(\\s*f[\"\\'].*{.*}.*[\"\\']", "code": "execute(f\"PRAGMA table_info({table_name})\""}, {"file": "database\\fix_database.py", "line": 278, "pattern": "execute\\s*\\(\\s*f[\"\\'].*{.*}.*[\"\\']", "code": "execute(f\"ALTER TABLE items ADD COLUMN {column_name} {column_type}\""}, {"file": "database\\postgresql_manager.py", "line": 58, "pattern": "execute\\s*\\(\\s*[\"\\'].*%.*[\"\\']", "code": "execute(\n                \"SELECT 1 FROM pg_database WHERE datname = %s\""}, {"file": "database\\postgresql_manager.py", "line": 65, "pattern": "execute\\s*\\(\\s*f[\"\\'].*{.*}.*[\"\\']", "code": "execute(f\"CREATE DATABASE {self.config['database']}\""}, {"file": "services\\postgresql_sales_manager.py", "line": 182, "pattern": "execute\\s*\\(\\s*[\"\\'].*%.*[\"\\']", "code": "execute(\n                        \"SELECT name, current_stock FROM products WHERE id = %s\""}, {"file": "services\\postgresql_sales_manager.py", "line": 406, "pattern": "execute\\s*\\(\\s*[\"\\'].*%.*[\"\\']", "code": "execute(\"SELECT id FROM customers WHERE name = %s\""}, {"file": "services\\postgresql_sales_manager.py", "line": 473, "pattern": "execute\\s*\\(\\s*[\"\\'].*%.*[\"\\']", "code": "execute(\n                        \"SELECT name, current_stock FROM products WHERE id = %s\""}], "password_security": [{"file": "database\\database_manager.py", "line": 376, "issue": "استخدام تشفير ضعيف أو كلمة مرور مكشوفة", "code": "password = \"123\""}], "file_access_risks": [{"file": "run_control_panel_simple.py", "line": 24, "risk": "وصول غير آمن للملفات أو تنفيذ أوامر", "code": "os.system("}], "input_validation": [], "authentication_issues": [], "authorization_issues": [], "data_exposure_risks": []}, "performance_analysis": {"startup_time": 0, "memory_usage": {"rss": 29.92578125, "vms": 22.4140625, "percent": 0.18708368020290314}, "database_performance": {"avg_query_time": 1.0151863098144531, "max_query_time": 1.0449886322021484, "min_query_time": 0.9858608245849609, "total_queries": 3}, "ui_responsiveness": {}, "bottlenecks": [], "optimization_suggestions": [], "import_performance": {"ui.main_window": 1140.0411128997803, "database.database_manager": 0.0, "auth.auth_manager": 0.0, "themes.theme_manager": 0.0}}, "database_integrity": {"connection_test": true, "table_structure": {"users": {"columns": 9, "records": 3, "column_details": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "username", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "password_hash", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "full_name", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "email", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "role", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "is_active", "type": "BOOLEAN", "not_null": false, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}, {"name": "last_login", "type": "TIMESTAMP", "not_null": false, "primary_key": false}]}, "sqlite_sequence": {"columns": 2, "records": 4, "column_details": [{"name": "name", "type": "", "not_null": false, "primary_key": false}, {"name": "seq", "type": "", "not_null": false, "primary_key": false}]}, "products": {"columns": 12, "records": 12, "column_details": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "name", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "barcode", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "category", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "unit", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "cost_price", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "selling_price", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "min_stock", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "current_stock", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "description", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}, {"name": "is_active", "type": "BOOLEAN", "not_null": false, "primary_key": false}]}, "customers": {"columns": 9, "records": 0, "column_details": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "name", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "phone", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "email", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "address", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "credit_limit", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "current_balance", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "is_active", "type": "BOOLEAN", "not_null": false, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}]}, "suppliers": {"columns": 9, "records": 0, "column_details": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "name", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "phone", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "email", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "address", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "tax_number", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "current_balance", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}, {"name": "is_active", "type": "BOOLEAN", "not_null": false, "primary_key": false}]}, "sales_invoices": {"columns": 12, "records": 0, "column_details": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "invoice_number", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "customer_id", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "total_amount", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "discount_amount", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "tax_amount", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "net_amount", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "payment_status", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "invoice_date", "type": "TIMESTAMP", "not_null": false, "primary_key": false}, {"name": "due_date", "type": "TIMESTAMP", "not_null": false, "primary_key": false}, {"name": "notes", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "created_by", "type": "INTEGER", "not_null": false, "primary_key": false}]}, "sales_invoice_items": {"columns": 6, "records": 0, "column_details": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "invoice_id", "type": "INTEGER", "not_null": true, "primary_key": false}, {"name": "product_id", "type": "INTEGER", "not_null": true, "primary_key": false}, {"name": "quantity", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "unit_price", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "total_price", "type": "REAL", "not_null": true, "primary_key": false}]}, "purchase_invoices": {"columns": 12, "records": 0, "column_details": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "invoice_number", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "supplier_id", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "total_amount", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "discount_amount", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "tax_amount", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "net_amount", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "payment_status", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "invoice_date", "type": "TIMESTAMP", "not_null": false, "primary_key": false}, {"name": "due_date", "type": "TIMESTAMP", "not_null": false, "primary_key": false}, {"name": "notes", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "created_by", "type": "INTEGER", "not_null": false, "primary_key": false}]}, "purchase_invoice_items": {"columns": 6, "records": 0, "column_details": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "invoice_id", "type": "INTEGER", "not_null": true, "primary_key": false}, {"name": "product_id", "type": "INTEGER", "not_null": true, "primary_key": false}, {"name": "quantity", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "unit_price", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "total_price", "type": "REAL", "not_null": true, "primary_key": false}]}, "treasury_transactions": {"columns": 8, "records": 0, "column_details": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "transaction_type", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "amount", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "description", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "reference_type", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "reference_id", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "transaction_date", "type": "TIMESTAMP", "not_null": false, "primary_key": false}, {"name": "created_by", "type": "INTEGER", "not_null": false, "primary_key": false}]}, "inventory_movements": {"columns": 9, "records": 0, "column_details": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "product_id", "type": "INTEGER", "not_null": true, "primary_key": false}, {"name": "movement_type", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "quantity", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "reference_type", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "reference_id", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "notes", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "movement_date", "type": "TIMESTAMP", "not_null": false, "primary_key": false}, {"name": "created_by", "type": "INTEGER", "not_null": false, "primary_key": false}]}, "chart_of_accounts": {"columns": 14, "records": 36, "column_details": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "account_code", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "account_name", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "account_type", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "parent_account_id", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "account_level", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "is_main_account", "type": "BOOLEAN", "not_null": false, "primary_key": false}, {"name": "current_balance", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "debit_balance", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "credit_balance", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "account_nature", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "is_active", "type": "BOOLEAN", "not_null": false, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}, {"name": "created_by", "type": "INTEGER", "not_null": false, "primary_key": false}]}, "journal_entries": {"columns": 14, "records": 0, "column_details": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "entry_number", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "entry_date", "type": "DATE", "not_null": true, "primary_key": false}, {"name": "description", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "reference_type", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "reference_id", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "total_debit", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "total_credit", "type": "REAL", "not_null": true, "primary_key": false}, {"name": "is_balanced", "type": "BOOLEAN", "not_null": false, "primary_key": false}, {"name": "status", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}, {"name": "created_by", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "posted_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}, {"name": "posted_by", "type": "INTEGER", "not_null": false, "primary_key": false}]}, "journal_entry_details": {"columns": 8, "records": 0, "column_details": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "journal_entry_id", "type": "INTEGER", "not_null": true, "primary_key": false}, {"name": "account_id", "type": "INTEGER", "not_null": true, "primary_key": false}, {"name": "description", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "debit_amount", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "credit_amount", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "line_number", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}]}, "employees": {"columns": 15, "records": 0, "column_details": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "name", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "position", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "department", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "phone", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "email", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "hire_date", "type": "DATE", "not_null": false, "primary_key": false}, {"name": "termination_date", "type": "DATE", "not_null": false, "primary_key": false}, {"name": "salary", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "national_id", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "address", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "emergency_contact", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "is_active", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "created_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}, {"name": "updated_at", "type": "TIMESTAMP", "not_null": false, "primary_key": false}]}, "activity_logs": {"columns": 8, "records": 80, "column_details": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": true}, {"name": "user_id", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "action", "type": "TEXT", "not_null": true, "primary_key": false}, {"name": "table_name", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "record_id", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "old_values", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "new_values", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "timestamp", "type": "TIMESTAMP", "not_null": false, "primary_key": false}]}, "users_fixed": {"columns": 9, "records": 3, "column_details": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "username", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "password_hash", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "full_name", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "email", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "role", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "is_active", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "created_at", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "last_login", "type": "TEXT", "not_null": false, "primary_key": false}]}, "chart_of_accounts_fixed": {"columns": 14, "records": 36, "column_details": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "account_code", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "account_name", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "account_type", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "parent_account_id", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "account_level", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "is_main_account", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "current_balance", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "debit_balance", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "credit_balance", "type": "REAL", "not_null": false, "primary_key": false}, {"name": "account_nature", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "is_active", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "created_at", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "created_by", "type": "INTEGER", "not_null": false, "primary_key": false}]}, "activity_logs_fixed": {"columns": 8, "records": 1, "column_details": [{"name": "id", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "user_id", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "action", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "table_name", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "record_id", "type": "INTEGER", "not_null": false, "primary_key": false}, {"name": "old_values", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "new_values", "type": "TEXT", "not_null": false, "primary_key": false}, {"name": "timestamp", "type": "TEXT", "not_null": false, "primary_key": false}]}, "sqlite_stat1": {"columns": 3, "records": 19, "column_details": [{"name": "tbl", "type": "", "not_null": false, "primary_key": false}, {"name": "idx", "type": "", "not_null": false, "primary_key": false}, {"name": "stat", "type": "", "not_null": false, "primary_key": false}]}}, "data_integrity": {"issues": []}, "index_analysis": {"idx_products_name": {"table": "products"}, "idx_products_barcode": {"table": "products"}, "idx_products_category": {"table": "products"}, "idx_products_active": {"table": "products"}, "idx_sales_invoices_number": {"table": "sales_invoices"}, "idx_sales_invoices_date": {"table": "sales_invoices"}, "idx_sales_invoices_customer": {"table": "sales_invoices"}, "idx_sales_invoices_status": {"table": "sales_invoices"}, "idx_sales_items_invoice": {"table": "sales_invoice_items"}, "idx_sales_items_product": {"table": "sales_invoice_items"}, "idx_customers_name": {"table": "customers"}, "idx_customers_phone": {"table": "customers"}, "idx_customers_active": {"table": "customers"}, "idx_suppliers_name": {"table": "suppliers"}, "idx_suppliers_active": {"table": "suppliers"}, "idx_users_username": {"table": "users"}, "idx_users_role": {"table": "users"}, "idx_users_active": {"table": "users"}, "idx_accounts_code": {"table": "chart_of_accounts"}, "idx_accounts_name": {"table": "chart_of_accounts"}, "idx_accounts_type": {"table": "chart_of_accounts"}, "idx_accounts_parent": {"table": "chart_of_accounts"}, "idx_accounts_active": {"table": "chart_of_accounts"}, "idx_journal_entries_number": {"table": "journal_entries"}, "idx_journal_entries_date": {"table": "journal_entries"}, "idx_journal_entries_status": {"table": "journal_entries"}, "idx_journal_entries_reference": {"table": "journal_entries"}, "idx_journal_details_entry": {"table": "journal_entry_details"}, "idx_journal_details_account": {"table": "journal_entry_details"}, "idx_purchase_invoices_number": {"table": "purchase_invoices"}, "idx_purchase_invoices_supplier": {"table": "purchase_invoices"}, "idx_purchase_invoices_date": {"table": "purchase_invoices"}, "idx_purchase_invoices_status": {"table": "purchase_invoices"}, "idx_purchase_items_invoice": {"table": "purchase_invoice_items"}, "idx_purchase_items_product": {"table": "purchase_invoice_items"}, "idx_employees_name": {"table": "employees"}, "idx_employees_department": {"table": "employees"}, "idx_employees_position": {"table": "employees"}, "idx_employees_active": {"table": "employees"}}, "foreign_key_constraints": [], "orphaned_records": [], "data_consistency": {}, "backup_status": {}}, "ui_consistency": {"rtl_compliance": {"files_with_rtl": 37, "total_ui_files": 42, "rtl_patterns": [{"file": "ui\\accounts_tree_window.py", "indicator": "anchor=\"e\""}, {"file": "ui\\accounts_tree_window.py", "indicator": "arabic"}, {"file": "ui\\accounts_window.py", "indicator": "arabic"}, {"file": "ui\\add_items_window.py", "indicator": "anchor=\"e\""}, {"file": "ui\\add_items_window.py", "indicator": "arabic"}, {"file": "ui\\add_items_window.py", "indicator": "عربي"}, {"file": "ui\\advanced_financial_reports_window.py", "indicator": "anchor=\"e\""}, {"file": "ui\\advanced_financial_reports_window.py", "indicator": "arabic"}, {"file": "ui\\advanced_financial_reports_window.py", "indicator": "عربي"}, {"file": "ui\\advanced_sections.py", "indicator": "anchor=\"e\""}]}, "font_consistency": {"unique_fonts": 466, "font_usage": [{"file": "ui\\accounts_tree_window.py", "font": "),\n            text_color="}, {"file": "ui\\accounts_tree_window.py", "font": "], 12),\n            text_color="}, {"file": "ui\\accounts_tree_window.py", "font": "], 12),\n            text_color="}, {"file": "ui\\accounts_tree_window.py", "font": ")\n        )\n        tree_title.pack(pady=(10, 5))\n\n        # شريط البحث\n        search_frame = ctk.CTkFrame(tree_frame, fg_color="}, {"file": "ui\\accounts_tree_window.py", "font": ")\n        )\n        details_title.pack(pady=(10, 20))\n\n        # إطار النموذج\n        form_frame = ctk.CTkScrollableFrame(parent)\n        form_frame.pack(fill="}, {"file": "ui\\accounts_tree_window.py", "font": ", pady=(5, 0))\n        self.name_entry = ctk.CTkEntry(parent, placeholder_text="}, {"file": "ui\\accounts_tree_window.py", "font": ", pady=(5, 0))\n        self.code_entry = ctk.CTkEntry(parent, placeholder_text="}, {"file": "ui\\accounts_tree_window.py", "font": ", pady=(5, 0))\n        self.type_combo = ctk.CTkComboBox(\n            parent,\n            values=["}, {"file": "ui\\accounts_tree_window.py", "font": ", pady=(5, 0))\n        self.parent_combo = ctk.CTkComboBox(parent, values=["}, {"file": "ui\\accounts_tree_window.py", "font": ", pady=(5, 0))\n        self.description_text = ctk.CTkTextbox(parent, height=80)\n        self.description_text.pack(fill="}]}, "color_scheme": {"unique_colors": 872, "color_usage": [{"file": "ui\\accounts_tree_window.py", "color": "])\n\n        # جعل النافذة في المقدمة\n        self.window.transient(self.parent)\n        self.window.grab_set()\n\n        # إنشاء المحتوى\n        self.create_header()\n        self.create_main_content()\n        self.create_buttons()\n\n        # تحميل البيانات\n        self.load_accounts_tree()\n\n    def create_header(self):\n        "}, {"file": "ui\\accounts_tree_window.py", "color": "])\n        header_frame.pack(fill="}, {"file": "ui\\accounts_tree_window.py", "color": "\n        )\n        title_label.pack(side="}, {"file": "ui\\accounts_tree_window.py", "color": ")\n        info_frame.pack(side="}, {"file": "ui\\accounts_tree_window.py", "color": "\n        )\n        self.accounts_count_label.pack()\n\n        self.tree_depth_label = ctk.CTkLabel(\n            info_frame,\n            text="}, {"file": "ui\\accounts_tree_window.py", "color": "\n        )\n        self.tree_depth_label.pack()\n\n    def create_main_content(self):\n        "}, {"file": "ui\\accounts_tree_window.py", "color": ")\n        main_frame.pack(fill="}, {"file": "ui\\accounts_tree_window.py", "color": ")\n        search_frame.pack(fill="}, {"file": "ui\\accounts_tree_window.py", "color": ")\n        buttons_frame.pack(fill="}, {"file": "ui\\accounts_tree_window.py", "color": "],\n            width=120\n        )\n        add_btn.pack(side="}]}, "layout_patterns": {}, "accessibility": {}, "responsive_design": {}}, "business_logic_validation": {"accounting_rules": {"balance_tests": [{"test_case": 1, "is_balanced": true, "total_debit": 1000, "total_credit": 1000, "difference": 0}, {"test_case": 2, "is_balanced": false, "total_debit": 1000, "total_credit": 900, "difference": 100}, {"test_case": 3, "is_balanced": true, "total_debit": 800, "total_credit": 800, "difference": 0}]}, "data_validation": {}, "workflow_integrity": {}, "calculation_accuracy": {"balance_accuracy": 100.0}, "audit_trail": {}}, "dependency_analysis": {}, "architecture_review": {}, "rtl_compliance": {}, "error_handling": {}}, "critical_issues": [], "warnings": [], "recommendations": []}