#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لبرنامج المحاسبة العربي
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox

# إعداد المظهر
ctk.set_appearance_mode("light")
ctk.set_default_color_theme("blue")

class SimpleControlPanel:
    """لوحة تحكم مبسطة للاختبار"""
    
    def __init__(self):
        # إنشاء النافذة الرئيسية
        self.window = ctk.CTk()
        self.window.title("🎛️ لوحة التحكم المبسطة - اختبار")
        self.window.geometry("800x600")
        
        # إنشاء الواجهة
        self.create_interface()
        
    def create_interface(self):
        """إنشاء واجهة المستخدم"""
        # عنوان رئيسي
        title_label = ctk.CTkLabel(
            self.window,
            text="🏢 برنامج ست الكل للمحاسبة",
            font=("Arial", 24, "bold"),
            text_color="#2E8B57"
        )
        title_label.pack(pady=20)
        
        # رسالة ترحيب
        welcome_label = ctk.CTkLabel(
            self.window,
            text="مرحباً بك في برنامج المحاسبة العربي",
            font=("Arial", 16),
            text_color="#333333"
        )
        welcome_label.pack(pady=10)
        
        # إطار الأزرار
        buttons_frame = ctk.CTkFrame(self.window)
        buttons_frame.pack(pady=20, padx=20, fill="both", expand=True)
        
        # أزرار الوظائف الرئيسية
        buttons = [
            {"text": "📊 المحاسبة الرئيسية", "command": self.open_accounting},
            {"text": "🛒 المبيعات", "command": self.open_sales},
            {"text": "📦 المشتريات", "command": self.open_purchases},
            {"text": "📋 المخزون", "command": self.open_inventory},
            {"text": "👥 العملاء", "command": self.open_customers},
            {"text": "📈 التقارير", "command": self.open_reports},
            {"text": "⚙️ الإعدادات", "command": self.open_settings},
            {"text": "❌ خروج", "command": self.exit_app}
        ]
        
        for i, btn_info in enumerate(buttons):
            btn = ctk.CTkButton(
                buttons_frame,
                text=btn_info["text"],
                font=("Arial", 14, "bold"),
                height=40,
                command=btn_info["command"]
            )
            btn.pack(pady=5, padx=20, fill="x")
        
        # شريط الحالة
        self.status_label = ctk.CTkLabel(
            self.window,
            text="✅ البرنامج جاهز للاستخدام",
            font=("Arial", 12),
            text_color="#28a745"
        )
        self.status_label.pack(side="bottom", pady=10)
    
    def update_status(self, message):
        """تحديث شريط الحالة"""
        self.status_label.configure(text=message)
    
    def open_accounting(self):
        """فتح نافذة المحاسبة الرئيسية"""
        messagebox.showinfo("المحاسبة الرئيسية", "سيتم فتح نافذة المحاسبة الرئيسية")
        self.update_status("📊 تم النقر على المحاسبة الرئيسية")
    
    def open_sales(self):
        """فتح نافذة المبيعات"""
        messagebox.showinfo("المبيعات", "سيتم فتح نافذة المبيعات")
        self.update_status("🛒 تم النقر على المبيعات")
    
    def open_purchases(self):
        """فتح نافذة المشتريات"""
        messagebox.showinfo("المشتريات", "سيتم فتح نافذة المشتريات")
        self.update_status("📦 تم النقر على المشتريات")
    
    def open_inventory(self):
        """فتح نافذة المخزون"""
        messagebox.showinfo("المخزون", "سيتم فتح نافذة المخزون")
        self.update_status("📋 تم النقر على المخزون")
    
    def open_customers(self):
        """فتح نافذة العملاء"""
        messagebox.showinfo("العملاء", "سيتم فتح نافذة العملاء")
        self.update_status("👥 تم النقر على العملاء")
    
    def open_reports(self):
        """فتح نافذة التقارير"""
        messagebox.showinfo("التقارير", "سيتم فتح نافذة التقارير")
        self.update_status("📈 تم النقر على التقارير")
    
    def open_settings(self):
        """فتح نافذة الإعدادات"""
        messagebox.showinfo("الإعدادات", "سيتم فتح نافذة الإعدادات")
        self.update_status("⚙️ تم النقر على الإعدادات")
    
    def exit_app(self):
        """إغلاق البرنامج"""
        if messagebox.askyesno("تأكيد الخروج", "هل تريد إغلاق البرنامج؟"):
            self.window.quit()
    
    def run(self):
        """تشغيل البرنامج"""
        self.window.mainloop()

if __name__ == "__main__":
    print("🎛️ تشغيل لوحة التحكم المبسطة...")
    print("=" * 50)
    
    try:
        app = SimpleControlPanel()
        app.run()
        print("✅ تم إغلاق البرنامج بنجاح")
    except Exception as e:
        print(f"❌ خطأ في تشغيل البرنامج: {e}")
        import traceback
import traceback
        traceback.print_exc()
